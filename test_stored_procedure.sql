/*
Name: sp_GetUserData
Objective: Retrieve user data based on status
Created by: <PERSON>
Created Date: 15-Jan-2024
Modified by: <PERSON>
Modified Date: 20-Jan-2024
Modification purpose: Added status filter
Input Parameters: @Status NVARCHAR(20)
Output Parameters: UserId, UserN<PERSON>, Email, Status
Tables Used: UserAccounts, UserProfiles
*/

CREATE PROCEDURE sp_GetUserData
    @Status NVARCHAR(20)
AS
BEGIN
    SELECT 
        u.UserId,
        u.UserName,
        u.Email,
        u.Status
    FROM UserAccounts u
    INNER JOIN UserProfiles p ON u.UserId = p.UserId
    WHERE u.Status = @Status
    ORDER BY u.UserName
END
